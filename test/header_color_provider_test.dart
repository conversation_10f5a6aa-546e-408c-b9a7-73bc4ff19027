import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/provider/header_color_provider.dart';

/// HeaderColorProvider测试
void main() {
  group('HeaderColorProvider 测试', () {
    late HeaderColorProvider provider;

    setUp(() {
      provider = HeaderColorProvider();
    });

    test('初始状态应该没有自定义颜色', () {
      expect(provider.hasCustomColor, isFalse);
      expect(provider.headerColor, isNull);
      expect(provider.textColor, isNull);
      expect(provider.iconBrightness, isNull);
    });

    test('设置头部颜色应该正确计算相关属性', () {
      const testColor = Colors.red;

      provider.setHeaderColor(testColor);

      expect(provider.hasCustomColor, isTrue);
      expect(provider.headerColor, equals(testColor));
      expect(provider.textColor, isNotNull);
      expect(provider.iconBrightness, isNotNull);
    });

    test('重置颜色应该清除所有自定义设置', () {
      // 先设置一个颜色
      provider.setHeaderColor(Colors.blue);
      expect(provider.hasCustomColor, isTrue);

      // 然后重置
      provider.resetColor();

      expect(provider.hasCustomColor, isFalse);
      expect(provider.headerColor, isNull);
      expect(provider.textColor, isNull);
      expect(provider.iconBrightness, isNull);
    });

    test('亮色背景应该使用深色文字和图标', () {
      const lightColor = Colors.white;

      provider.setHeaderColor(lightColor);

      expect(provider.textColor, equals(Colors.black87));
      expect(provider.iconBrightness, equals(Brightness.dark));
    });

    test('暗色背景应该使用浅色文字和图标', () {
      const darkColor = Colors.black;

      provider.setHeaderColor(darkColor);

      expect(provider.textColor, equals(Colors.white));
      expect(provider.iconBrightness, equals(Brightness.light));
    });

    test('applyStatusBarColorForHomeTab应该根据topIndex正确应用颜色', () {
      // 设置一个测试颜色
      provider.setHeaderColor(Colors.red);

      // 测试增长榜页面（index 0）- 应该应用自定义颜色
      provider.applyStatusBarColorForHomeTab(0);
      // 这里无法直接测试SystemChrome的调用，但可以验证方法不抛异常

      // 测试新途径圈页面（index 1）- 应该使用透明状态栏
      provider.applyStatusBarColorForHomeTab(1);

      // 测试同行朋友圈页面（index 2）- 应该应用自定义颜色
      provider.applyStatusBarColorForHomeTab(2);

      // 如果没有抛出异常，说明方法执行正常
      expect(true, isTrue);
    });

    test('没有自定义颜色时applyStatusBarColorForHomeTab应该使用默认颜色', () {
      // 确保没有自定义颜色
      provider.resetColor();

      // 测试增长榜页面 - 应该使用默认颜色
      provider.applyStatusBarColorForHomeTab(0);

      // 测试同行朋友圈页面 - 应该使用默认颜色
      provider.applyStatusBarColorForHomeTab(2);

      // 如果没有抛出异常，说明方法执行正常
      expect(true, isTrue);
    });
  });
}
