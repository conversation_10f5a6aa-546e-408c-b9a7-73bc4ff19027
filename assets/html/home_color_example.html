<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home页面颜色测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
        }

        .section {
            margin-bottom: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .section h2 {
            margin: 0 0 16px 0;
            color: #495057;
            font-size: 18px;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .color-button {
            padding: 12px;
            border: none;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s;
            font-size: 14px;
        }

        .color-button:hover {
            transform: translateY(-2px);
        }

        .custom-color {
            display: flex;
            gap: 8px;
            align-items: center;
            margin-top: 12px;
        }

        .color-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .apply-button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .reset-button {
            width: 100%;
            padding: 12px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin-top: 16px;
        }

        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 12px;
            margin-top: 16px;
            font-size: 12px;
            color: #1565c0;
        }

        .result {
            margin-top: 16px;
            padding: 12px;
            background: #f1f3f4;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🏠 Home页面颜色测试</h1>

        <div class="section">
            <h2>预设颜色</h2>
            <div class="color-grid">
                <button class="color-button" style="background: #ff4757;"
                    onclick="changeHomeColor('#ff4757')">红色</button>
                <button class="color-button" style="background: #2ed573;"
                    onclick="changeHomeColor('#2ed573')">绿色</button>
                <button class="color-button" style="background: #3742fa;"
                    onclick="changeHomeColor('#3742fa')">蓝色</button>
                <button class="color-button" style="background: #ffa502;"
                    onclick="changeHomeColor('#ffa502')">橙色</button>
                <button class="color-button" style="background: #a4b0be;"
                    onclick="changeHomeColor('#a4b0be')">灰色</button>
                <button class="color-button" style="background: #2f3542;"
                    onclick="changeHomeColor('#2f3542')">深色</button>
            </div>

            <div class="custom-color">
                <input type="color" id="colorPicker" class="color-input" value="#667eea">
                <button class="apply-button" onclick="applyCustomColor()">应用自定义颜色</button>
            </div>
        </div>

        <div class="section">
            <h2>控制操作</h2>
            <button class="reset-button" onclick="resetHomeColor()">重置为默认颜色</button>

            <div class="info">
                <strong>功能说明：</strong><br>
                • 点击预设颜色按钮可快速更改Home页面顶部背景颜色和_topTabView字体颜色<br>
                • 使用颜色选择器可以自定义任意颜色<br>
                • 重置按钮可恢复默认Home页面颜色<br>
                • 只在首页时应用自定义颜色，切换到其他页面时重置<br>
                • 切换回首页时会保持之前设置的颜色<br>
                • <strong>新逻辑：</strong><br>
                • "增长榜"和"同行朋友圈"：使用WebView设置的颜色（状态栏+背景+字体）<br>
                • "新途径圈"：直接使用默认颜色，状态栏设置为透明<br>
                • <strong>WebView刷新：</strong>切换到"增长榜"或"同行朋友圈"时会自动刷新WebView
            </div>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Home页面颜色变更函数
        function changeHomeColor(color) {
            const message = {
                action: 'headerColorChange',
                headerColor: color
            };

            // 发送消息给Flutter
            if (window.xtjrChannel) {
                window.xtjrChannel.postMessage(JSON.stringify(message));
            }

            showResult(`发送Home页面颜色变更消息: ${color}`);
            console.log('Home页面颜色变更:', message);
        }

        // 应用自定义颜色
        function applyCustomColor() {
            const colorPicker = document.getElementById('colorPicker');
            const color = colorPicker.value;
            changeHomeColor(color);
        }

        // 重置Home页面颜色
        function resetHomeColor() {
            changeHomeColor('0x00000000');
            showResult('重置Home页面颜色为默认值');
        }

        // 显示结果
        function showResult(message) {
            const resultDiv = document.getElementById('result');
            const timestamp = new Date().toLocaleTimeString();
            resultDiv.textContent = `[${timestamp}] ${message}`;
            resultDiv.style.display = 'block';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function () {
            showResult('页面加载完成，可以开始测试Home页面颜色功能');
        });
    </script>
</body>

</html>