# Home页面动态颜色变更功能

## 功能概述

本功能使用Provider插件实现WebView页面通过JavaScript消息动态改变Home页面顶部背景颜色和_topTabView字体颜色，同时管理状态栏颜色的显示逻辑。

## 功能特性

- ✅ 使用Provider插件管理颜色状态，支持响应式UI更新
- ✅ 支持WebView页面动态设置Home页面顶部背景颜色
- ✅ 支持动态设置_topTabView中的字体颜色
- ✅ 只在"增长榜"页面时应用背景和字体颜色变化
- ✅ 只在首页时应用状态栏颜色，切换到其他页面时重置
- ✅ 切换回首页时保持之前设置的颜色
- ✅ 自动根据颜色亮度调整状态栏图标颜色和文字颜色
- ✅ 颜色状态持久化保存

## 使用方法

### 1. WebView页面发送消息

在WebView的HTML页面中，通过JavaScript发送消息来改变Home页面颜色：

```javascript
// 改变全局状态栏颜色
function changeHomeColor(color) {
    const message = {
        action: 'headerColorChange',
        headerColor: color  // 支持 #RRGGBB 或 #AARRGGBB 格式
    };
    
    if (window.flutter_inappwebview) {
        window.flutter_inappwebview.callHandler('onJSMessage', JSON.stringify(message));
    }
}

// 示例用法
changeHomeColor('#ff4757'); // 红色
changeHomeColor('#2ed573'); // 绿色
changeHomeColor('#3742fa'); // 蓝色
```

### 2. 支持的颜色格式

- `#RRGGBB` - 6位十六进制颜色（如：#ff4757）
- `#AARRGGBB` - 8位十六进制颜色，包含透明度（如：#80ff4757）
- `transparent` - 透明色，等同于重置

### 3. 自动重置机制

全局状态栏颜色会在以下情况自动重置为默认值：

- 退出WebView页面

## 技术实现

### 架构设计

```
WebView (JavaScript)
    ↓ 发送消息
WebviewScreenPage
    ↓ 解析颜色并直接设置
SystemChrome.setSystemUIOverlayStyle()
    ↓ 应用全局状态栏样式
整个应用的状态栏颜色
```

### 核心组件

1. **WebviewScreenPage** - 处理WebView消息并直接设置状态栏颜色的页面
2. **SystemChrome** - Flutter系统UI控制API，用于设置全局状态栏样式

### 文件结构

```
lib/
├── provider/home_color_bloc/
│   ├── home_color_bloc.dart      # Home页面颜色BLoC
│   ├── home_color_event.dart     # Home页面颜色事件
│   └── home_color_state.dart     # Home页面颜色状态
├── manager/bloc_manager.dart     # BLoC管理器
├── common/page/webview_screen_page.dart  # WebView页面
└── page/home/<USER>

assets/html/
└── home_color_example.html       # 测试示例页面

test/
└── home_color_bloc_test.dart     # 单元测试
```

## 实现细节

### 1. Scaffold状态栏控制

使用Scaffold的AppBar来控制状态栏颜色，根据_currentTopIndex动态切换：

```dart
Scaffold(
  appBar: PreferredSize(
    preferredSize: Size.zero,
    child: AppBar(
      backgroundColor: _currentTopIndex == 0
          ? _currentHomeColorState.statusBarColor
          : Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: _currentTopIndex == 0
            ? _currentHomeColorState.statusBarColor
            : Colors.transparent,
        statusBarIconBrightness: _currentTopIndex == 0
            ? _currentHomeColorState.iconBrightness
            : Brightness.dark,
      ),
    ),
  ),
  body: // 页面内容
)
```

### 2. _topTabView文字颜色

动态应用文字颜色到_topTabView中的文本，只在"增长榜"页面生效：

```dart
Text(tabTitle,
  style: TextStyle(
    color: _currentTopIndex == 0 && !_currentHomeColorState.isDefault
        ? _currentHomeColorState.textColor
        : AppTheme.colorBlackTitle,
    fontSize: 22.sp
  )
)
```

## 测试

### 运行单元测试

```bash
flutter test test/home_color_bloc_test.dart
```

### 使用示例页面测试

1. 在应用中打开WebView页面
2. 加载 `assets/html/home_color_example.html`
3. 点击不同颜色按钮测试Home页面颜色变更
4. 退出WebView验证自动重置功能

## 注意事项

1. **页面隔离**：只影响Home页面，不影响其他页面
2. **Tab切换逻辑**：
   - "增长榜"（index 0）和"同行朋友圈"（index 2）：使用WebView设置的颜色
   - "新途径圈"（index 1）：直接使用默认颜色，状态栏设置为透明
3. **颜色格式**：确保传入的颜色字符串格式正确
4. **性能考虑**：频繁的颜色变更可能影响性能，建议适度使用
5. **平台差异**：iOS和Android在状态栏处理上可能有细微差异
6. **状态管理**：使用Provider模式，确保UI响应式更新

## 故障排除

### 常见问题

1. **颜色不生效**
   - 检查颜色格式是否正确
   - 确认WebView消息是否正确发送
   - 检查HomeColorBloc是否正确接收事件

2. **退出WebView后颜色未重置**
   - 检查dispose方法是否正确调用ResetHomeColorEvent
   - 确认HomeColorBloc订阅是否正确设置

3. **文字颜色不变化**
   - 检查_topTabView中的颜色逻辑
   - 确认_currentHomeColorState是否正确更新

### 调试方法

1. 查看控制台日志中的Home页面颜色变更消息
2. 使用Flutter Inspector检查HomeColorBloc状态
3. 在测试页面中查看消息发送结果

## 扩展功能

未来可以考虑添加的功能：

- 支持更多UI组件的颜色变更
- 支持渐变色背景
- 支持动画过渡效果
- 支持主题模式切换
