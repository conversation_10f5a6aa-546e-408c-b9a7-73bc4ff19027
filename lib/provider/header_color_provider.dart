import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';

/// 头部颜色管理Provider
class HeaderColorProvider extends ChangeNotifier {
  Color? _headerColor;
  Color? _textColor;
  Brightness? _iconBrightness;

  /// 当前头部颜色
  Color? get headerColor => _headerColor;

  /// 当前文字颜色
  Color? get textColor => _textColor;

  /// 当前图标亮度
  Brightness? get iconBrightness => _iconBrightness;

  /// 是否有自定义颜色
  bool get hasCustomColor => _headerColor != null;

  HeaderColorProvider() {
    _initFromGlobalPreferences();
  }

  /// 从全局配置初始化颜色
  void _initFromGlobalPreferences() {
    String? colorString = GlobalPreferences().webHeaderColor;
    if (colorString != null && colorString != "0x00000000") {
      Color color = _parseColorString(colorString);
      _setColors(color);
    }
  }

  /// 解析颜色字符串
  Color _parseColorString(String colorString) {
    // 移除0x前缀
    String hexColor = colorString.replaceAll('0x', '').replaceAll('#', '');

    // 如果是6位，添加FF作为alpha值
    if (hexColor.length == 6) {
      hexColor = 'FF$hexColor';
    }

    // 解析为整数并创建Color
    return Color(int.parse(hexColor, radix: 16));
  }

  /// 设置头部颜色
  void setHeaderColor(Color color) {
    _setColors(color);

    // 保存到全局配置
    GlobalPreferences().webHeaderColor =
        "0x${color.value.toRadixString(16).padLeft(8, '0')}";

    notifyListeners();
  }

  /// 重置颜色为默认值
  void resetColor() {
    _headerColor = null;
    _textColor = null;
    _iconBrightness = null;

    // 清除全局配置
    GlobalPreferences().webHeaderColor = "0x00000000";

    notifyListeners();
  }

  /// 设置颜色并计算相关属性
  void _setColors(Color color) {
    _headerColor = color;

    // 根据颜色亮度计算文字颜色和图标亮度
    double luminance = color.computeLuminance();
    _textColor = luminance > 0.5 ? Colors.black87 : Colors.white;
    _iconBrightness = luminance > 0.5 ? Brightness.dark : Brightness.light;
  }

  /// 应用状态栏颜色（只在需要时调用）
  void applyStatusBarColor({bool apply = true}) {
    if (apply && _headerColor != null) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
        statusBarColor: _headerColor,
        statusBarIconBrightness: _iconBrightness ?? Brightness.dark,
        statusBarBrightness: Platform.isAndroid
            ? (_iconBrightness == Brightness.dark
                ? Brightness.light
                : Brightness.dark)
            : null,
      ));
    } else {
      // 重置为默认状态栏
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ));
    }
  }

  /// 根据Home页面内部Tab切换应用状态栏颜色
  /// topIndex: 0=增长榜, 1=新途径圈, 2=同行朋友圈
  void applyStatusBarColorForHomeTab(int topIndex) {
    if (topIndex == 0 || topIndex == 2) {
      // 增长榜和同行朋友圈：使用自定义颜色（如果有的话）
      if (_headerColor != null) {
        SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
          statusBarColor: _headerColor,
          statusBarIconBrightness: _iconBrightness ?? Brightness.dark,
          statusBarBrightness: Platform.isAndroid
              ? (_iconBrightness == Brightness.dark
                  ? Brightness.light
                  : Brightness.dark)
              : null,
        ));
      } else {
        // 没有自定义颜色时使用默认
        SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ));
      }
    } else if (topIndex == 1) {
      // 新途径圈：直接使用透明状态栏
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
      ));
    }
  }
}
