import 'dart:io';
import 'package:flutter/foundation.dart';

/// 桌面端网络辅助工具
class DesktopNetworkHelper {
  static const String _testUrl = 'https://api.xtjzx.cn';

  /// 测试网络连接
  static Future<bool> testNetworkConnection() async {
    if (!_isDesktop()) {
      return true; // 移动端直接返回 true
    }

    try {
      debugPrint('Testing network connection to $_testUrl');

      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 10);
      client.badCertificateCallback = (cert, host, port) => true;

      final request = await client.getUrl(Uri.parse('$_testUrl/health'));
      request.headers
          .set('User-Agent', 'npemployee/1.0 (${Platform.operatingSystem})');

      final response = await request.close();
      final success = response.statusCode == 200 ||
          response.statusCode == 404; // 404 也算连接成功

      debugPrint(
          'Network test result: ${response.statusCode}, success: $success');
      client.close();

      return success;
    } catch (e) {
      debugPrint('Network test failed: $e');
      return false;
    }
  }

  /// 获取网络诊断信息
  static Future<Map<String, dynamic>> getNetworkDiagnostics() async {
    final diagnostics = <String, dynamic>{
      'platform': Platform.operatingSystem,
      'isDesktop': _isDesktop(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    if (!_isDesktop()) {
      diagnostics['status'] = 'mobile_platform';
      return diagnostics;
    }

    try {
      // 测试基本连接
      final basicTest = await testNetworkConnection();
      diagnostics['basic_connection'] = basicTest;

      // 测试 DNS 解析
      final dnsTest = await _testDnsResolution();
      diagnostics['dns_resolution'] = dnsTest;

      // 测试 HTTPS 连接
      final httpsTest = await _testHttpsConnection();
      diagnostics['https_connection'] = httpsTest;
    } catch (e) {
      diagnostics['error'] = e.toString();
    }

    return diagnostics;
  }

  /// 测试 DNS 解析
  static Future<bool> _testDnsResolution() async {
    try {
      final addresses = await InternetAddress.lookup('api.xtjzx.cn');
      debugPrint(
          'DNS resolution successful: ${addresses.map((a) => a.address).join(', ')}');
      return addresses.isNotEmpty;
    } catch (e) {
      debugPrint('DNS resolution failed: $e');
      return false;
    }
  }

  /// 测试 HTTPS 连接
  static Future<bool> _testHttpsConnection() async {
    try {
      final client = HttpClient();
      client.connectionTimeout = const Duration(seconds: 10);
      client.badCertificateCallback = (cert, host, port) {
        debugPrint('Certificate for $host:$port - Subject: ${cert.subject}');
        return true; // 开发环境允许所有证书
      };

      final request = await client.getUrl(Uri.parse('https://api.xtjzx.cn'));
      request.headers
          .set('User-Agent', 'npemployee/1.0 (${Platform.operatingSystem})');

      final response = await request.close();
      final success = response.statusCode < 500; // 5xx 错误才算失败

      debugPrint(
          'HTTPS test result: ${response.statusCode}, success: $success');
      client.close();

      return success;
    } catch (e) {
      debugPrint('HTTPS test failed: $e');
      return false;
    }
  }

  /// 检查是否为桌面平台
  static bool _isDesktop() {
    return Platform.isMacOS || Platform.isWindows || Platform.isLinux;
  }

  /// 获取网络错误的用户友好提示
  static String getNetworkErrorMessage(dynamic error) {
    if (!_isDesktop()) {
      return '网络连接失败，请检查网络设置';
    }

    final errorStr = error.toString().toLowerCase();

    if (errorStr.contains('operation not permitted')) {
      return '网络权限被拒绝，请检查应用权限设置';
    } else if (errorStr.contains('connection refused')) {
      return '服务器拒绝连接，请稍后重试';
    } else if (errorStr.contains('timeout')) {
      return '网络连接超时，请检查网络状况';
    } else if (errorStr.contains('certificate')) {
      return '证书验证失败，请检查系统时间设置';
    } else if (errorStr.contains('host not found') ||
        errorStr.contains('name resolution')) {
      return 'DNS 解析失败，请检查网络设置';
    } else {
      return '网络连接失败：$error';
    }
  }
}
