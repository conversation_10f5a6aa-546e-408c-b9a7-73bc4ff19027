import 'package:flutter/material.dart';

extension HexColor on String {
  Color toColor() {
    String hex = replaceAll('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex';
    }
    if (hex.length == 8) {
      return Color(int.parse(hex, radix: 16));
    }
    return Colors.transparent;
  }
}

extension AlibabaPuHuiTi on TextStyle {
  TextStyle get phBold {
    return copyWith(fontFamily: 'AlibabaPuHuiTi', fontWeight: FontWeight.bold);
  }

  TextStyle get phMedium {
    return copyWith(fontFamily: 'AlibabaPuHuiTi', fontWeight: FontWeight.w500);
  }

  TextStyle get phHeavy {
    return copyWith(fontFamily: 'AlibabaPuHuiTi', fontWeight: FontWeight.w800);
  }

  TextStyle get phRegular {
    return copyWith(fontFamily: 'AlibabaPuHuiTi', fontWeight: FontWeight.w400);
  }

  TextStyle get phLight {
    return copyWith(fontFamily: 'AlibabaPuHuiTi', fontWeight: FontWeight.w300);
  }
}

extension PingFang on TextStyle {
  TextStyle get pfBold {
    return copyWith(fontFamily: 'PingFang', fontWeight: FontWeight.bold);
  }

  TextStyle get pfSemiBold {
    return copyWith(fontFamily: 'PingFang', fontWeight: FontWeight.w600);
  }

  TextStyle get pfMedium {
    return copyWith(fontFamily: 'PingFang', fontWeight: FontWeight.w500);
  }

  TextStyle get pfRegular {
    return copyWith(fontFamily: 'PingFang', fontWeight: FontWeight.w400);
  }

  TextStyle get pfLight {
    return copyWith(fontFamily: 'PingFang', fontWeight: FontWeight.w300);
  }
}
